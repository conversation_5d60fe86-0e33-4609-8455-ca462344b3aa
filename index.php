<?php
use system\router;
use system\startup_sequence;

header("Cache-Control: no-store, no-cache, must-revalidate"); // HTTP/1.1
header("Cache-Control: post-check=0, pre-check=0", false);
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // Date in the past
header("Pragma: no-cache"); // HTTP/1.0
header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");

$path['fs_app_root'] = __DIR__ . '/';
$schema = require_once $path['fs_app_root'] . 'sys/config/path_schema.php';
$path['fs_system'] = "{$path['fs_app_root']}/{$schema['system']['root']}" ;

require_once $path['fs_system'] . 'classes/startup_sequence.class.php';

startup_sequence::start($schema);

print_rr("starting route");
echo router::route();
include('resources/components/modal.php');